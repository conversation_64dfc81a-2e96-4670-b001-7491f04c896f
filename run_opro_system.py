#!/usr/bin/env python3
"""
OPRO系统运行脚本 (OPRO System Runner)

用于运行集成了OPRO优化功能的多智能体交易系统。

使用示例:
    # 默认运行（OPRO优化模式）
    python run_opro_system.py --provider zhipuai

    # 运行基础评估（不含OPRO）
    python run_opro_system.py --provider zhipuai --mode evaluation --disable-opro

    # 运行OPRO优化循环（默认模式）
    python run_opro_system.py --provider zhipuai --mode optimization

    # 运行完整集成（评估+优化）
    python run_opro_system.py --provider zhipuai --mode integrated

    # 获取OPRO仪表板数据
    python run_opro_system.py --provider zhipuai --mode dashboard
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface

def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO
    
    handlers: List[logging.Handler] = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )
    
    return logging.getLogger(__name__)

def load_config(config_path: str = "config/opro_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print(f"配置文件不存在: {config_path}")
            return {}
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {}

def create_system_config(args) -> Dict[str, Any]:
    """创建系统配置"""
    return {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": ["AAPL"],  # 可以通过参数扩展
        "starting_cash": 1000000,
        "simulation_days": args.simulation_days,
        "verbose": args.verbose,
        "enable_concurrent_execution": not args.disable_concurrent,
        "fail_on_large_gaps": False,
        "fill_date_gaps": True
    }

def run_evaluation_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行评估模式"""
    logger.info("=" * 80)
    logger.info("运行模式: 标准评估")
    logger.info("=" * 80)
    
    try:
        if args.quick_test:
            logger.info("运行快速测试...")
            result = assessor.run_quick_test()
        else:
            logger.info("运行完整评估...")
            result = assessor.run(
                target_agents=args.agents.split(',') if args.agents else None,
                max_coalitions=args.max_coalitions
            )
        
        return {
            "mode": "evaluation", 
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"评估模式执行失败: {e}")
        return {
            "mode": "evaluation",
            "success": False,
            "error": str(e)
        }

def run_optimization_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行优化模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO优化")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行优化模式")
        return {
            "mode": "optimization",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        target_agents = args.agents.split(',') if args.agents else None
        
        logger.info(f"开始OPRO优化循环...")
        result = assessor.run_opro_optimization_cycle(
            target_agents=target_agents,
            force_optimization=args.force_optimization
        )
        
        return {
            "mode": "optimization",
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"优化模式执行失败: {e}")
        return {
            "mode": "optimization",
            "success": False,
            "error": str(e)
        }

def run_integrated_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行集成模式（评估+优化）"""
    logger.info("=" * 80)
    logger.info("运行模式: 集成模式（评估+优化）")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行集成模式")
        return {
            "mode": "integrated",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        target_agents = args.agents.split(',') if args.agents else None
        
        result = assessor.run_with_opro_integration(
            target_agents=target_agents,
            max_coalitions=args.max_coalitions,
            run_optimization_before=args.optimize_before,
            run_optimization_after=args.optimize_after
        )
        
        return {
            "mode": "integrated",
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"集成模式执行失败: {e}")
        return {
            "mode": "integrated",
            "success": False,
            "error": str(e)
        }

def run_dashboard_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行仪表板模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO仪表板")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行仪表板模式")
        return {
            "mode": "dashboard",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        dashboard_data = assessor.get_opro_dashboard_data()
        
        # 输出仪表板数据
        logger.info("OPRO系统状态:")
        
        # 系统统计
        system_stats = dashboard_data.get("system_stats", {})
        if "optimizer" in system_stats:
            opt_stats = system_stats["optimizer"]
            logger.info(f"  优化器统计:")
            logger.info(f"    总优化次数: {opt_stats.get('total_optimizations', 0)}")
            logger.info(f"    成功优化次数: {opt_stats.get('successful_optimizations', 0)}")
            logger.info(f"    成功率: {opt_stats.get('success_rate', 0):.1f}%")
        
        # 智能体性能
        agent_performance = dashboard_data.get("agent_performance", {})
        if "ranking" in agent_performance:
            logger.info(f"  智能体性能排名:")
            for i, agent_id in enumerate(agent_performance["ranking"][:5]):
                stats = agent_performance["agent_stats"].get(agent_id, {})
                avg_score = stats.get("average_score", 0)
                logger.info(f"    {i+1}. {agent_id}: {avg_score:.6f}")
        
        # 优化建议
        recommendations = dashboard_data.get("recommendations", [])
        if recommendations:
            logger.info(f"  优化建议:")
            for rec in recommendations[:3]:
                priority = rec.get("priority", "medium")
                message = rec.get("message", "")
                logger.info(f"    [{priority.upper()}] {message}")
        
        return {
            "mode": "dashboard",
            "result": dashboard_data,
            "success": dashboard_data.get("opro_enabled", False)
        }
        
    except Exception as e:
        logger.error(f"仪表板模式执行失败: {e}")
        return {
            "mode": "dashboard",
            "success": False,
            "error": str(e)
        }

def export_results(result: Dict[str, Any], output_path: str, logger: logging.Logger):
    """导出结果到文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已导出至: {output_path}")
        
    except Exception as e:
        logger.error(f"导出结果失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO系统运行脚本")
    
    # 基础参数
    parser.add_argument("--provider", type=str, default="zhipuai", 
                       choices=["zhipuai", "openai"], help="LLM提供商")
    parser.add_argument("--mode", type=str, default="optimization",
                       choices=["evaluation", "optimization", "integrated", "dashboard"],
                       help="运行模式")
    parser.add_argument("--enable-opro", action="store_true", default=True, help="启用OPRO功能")
    parser.add_argument("--disable-opro", action="store_true", help="禁用OPRO功能")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # 系统配置
    parser.add_argument("--start-date", type=str, default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", type=str, default="2025-01-07", help="结束日期")
    parser.add_argument("--simulation-days", type=int, help="模拟天数")
    parser.add_argument("--agents", type=str, help="目标智能体列表（逗号分隔）")
    parser.add_argument("--max-coalitions", type=int, help="最大联盟数量")
    parser.add_argument("--disable-concurrent", action="store_true", help="禁用并发执行")
    
    # 测试选项
    parser.add_argument("--quick-test", action="store_true", help="运行快速测试")
    
    # 优化选项
    parser.add_argument("--force-optimization", action="store_true", help="强制优化")
    parser.add_argument("--optimize-before", action="store_true", help="评估前优化")
    parser.add_argument("--optimize-after", action="store_true", default=True, help="评估后优化")
    
    # 输出选项
    parser.add_argument("--output", type=str, help="结果输出文件")
    parser.add_argument("--log-file", type=str, help="日志文件")
    parser.add_argument("--export-opro-data", action="store_true", help="导出OPRO数据")
    
    # 配置文件
    parser.add_argument("--config", type=str, default="config/opro_config.json", 
                       help="配置文件路径")
    
    args = parser.parse_args()
    
    # 处理OPRO开关逻辑
    if args.disable_opro:
        args.enable_opro = False
    
    # 设置日志
    log_file = args.log_file or f"opro_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logger = setup_logging(args.verbose, log_file)
    
    logger.info("=" * 100)
    logger.info("OPRO系统启动")
    logger.info("=" * 100)
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"LLM提供商: {args.provider}")
    logger.info(f"OPRO启用: {args.enable_opro}")
    
    try:
        # 加载配置
        config_data = load_config(args.config)
        
        # 创建系统配置
        system_config = create_system_config(args)
        
        # 初始化系统
        logger.info("初始化系统...")
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider=args.provider, logger=logger)
        
        # 创建评估器
        opro_config = {}
        if config_data:
            # 合并所有相关的配置部分
            opro_config.update(config_data.get("optimization", {}))
            opro_config.update(config_data.get("evaluation", {}))
            opro_config.update(config_data.get("storage", {}))
        
        assessor = ContributionAssessor(
            config=system_config,
            logger=logger,
            llm_provider=args.provider,
            enable_opro=args.enable_opro,
            opro_config=opro_config
        )
        
        logger.info("系统初始化完成")
        
        # 根据模式运行
        if args.mode == "evaluation":
            result = run_evaluation_mode(assessor, args, logger)
        elif args.mode == "optimization":
            result = run_optimization_mode(assessor, args, logger)
        elif args.mode == "integrated":
            result = run_integrated_mode(assessor, args, logger)
        elif args.mode == "dashboard":
            result = run_dashboard_mode(assessor, args, logger)
        else:
            raise ValueError(f"未知运行模式: {args.mode}")
        
        # 添加执行信息
        result.update({
            "execution_info": {
                "timestamp": datetime.now().isoformat(),
                "mode": args.mode,
                "opro_enabled": args.enable_opro,
                "llm_provider": args.provider,
                "config_file": args.config
            }
        })
        
        # 输出结果
        success = result.get("success", False)
        logger.info("=" * 100)
        if success:
            logger.info("🎉 执行成功!")
        else:
            logger.error("❌ 执行失败!")
            if "error" in result:
                logger.error(f"错误: {result['error']}")
        logger.info("=" * 100)
        
        # 导出结果
        if args.output:
            export_results(result, args.output, logger)
        
        # 导出OPRO数据
        if args.export_opro_data and assessor.enable_opro:
            export_result = assessor.export_opro_data()
            if export_result.get("success", False):
                logger.info(f"OPRO数据已导出至: {export_result['export_directory']}")
        
        # 返回适当的退出代码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"系统执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()