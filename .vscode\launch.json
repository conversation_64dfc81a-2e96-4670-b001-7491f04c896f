{"version": "0.2.0", "configurations": [{"name": "Python: Run OPRO System", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_opro_system.py", "console": "integratedTerminal", "args": ["--mode", "optimization", "--verbose", "--start-date", "2025-01-01", "--end-date", "2025-01-31", "--enable-opro"]}, {"name": "Python: Validate Sequential System", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/validate_sequential_system.py", "console": "integratedTerminal", "args": []}, {"name": "Python: Test N+1 Optimization", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/test_n_plus_1_optimization.py", "console": "integratedTerminal", "args": ["--mode", "full", "--stocks", "AAPL", "--start-date", "2025-01-01", "--end-date", "2025-03-31", "--verbose"]}, {"name": "Python: Debug Experiment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/run_experiment.py", "console": "integratedTerminal", "args": ["--stocks", "AAPL", "--start-date", "2025-01-01", "--end-date", "2025-03-31", "--mode", "optimization_only", "--contribution-method", "n_plus_1"]}, {"name": "Python: Run N+1 Experiment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/run_experiment.py", "console": "integratedTerminal", "args": ["--stocks", "AAPL", "--start-date", "2025-01-01", "--end-date", "2025-03-31", "--contribution-method", "n_plus_1", "--optimization-strategy", "adaptive", "--verbose"]}, {"name": "Python: Test Mode Experiment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/run_experiment.py", "console": "integratedTerminal", "args": ["--stocks", "AAPL", "--start-date", "2025-01-01", "--end-date", "2025-01-31", "--contribution-method", "n_plus_1", "--optimization-strategy", "adaptive", "--test", "--verbose", "--coordinator-type", "standard"]}, {"name": "Python: Run Optimized Shapley Experiment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/scripts/run_experiment.py", "console": "integratedTerminal", "args": ["--stocks", "AAPL", "--start-date", "2024-01-01", "--end-date", "2024-03-31", "--mode", "full_optimization", "--contribution-method", "shapley", "--coordinator-type", "optimized", "--verbose"]}, {"name": "Python: Run Contribution Assessment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_contribution_assessment.py", "console": "integratedTerminal", "args": ["--llm-provider", "zhipuai", "--weekly-evaluation", "--start-date", "2025-01-01", "--end-date", "2025-01-15", "--verbose"]}, {"name": "Python: Run with Real Agents", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_with_real_agents.py", "console": "integratedTerminal", "args": ["--llm-provider", "zhipuai", "--verbose"]}, {"name": "Python: Run Daily LLM Trading", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/daily_llm_trading.py", "console": "integratedTerminal", "args": ["--llm-provider", "zhipuai", "--start-date", "2025-01-01", "--end-date", "2025-01-03", "--verbose"]}]}